/* Mobile-specific fixes and improvements */

/* Prevent horizontal scrolling */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Improve touch targets */
@media (max-width: 768px) {
  button, 
  .btn, 
  a[role="button"],
  input[type="submit"],
  input[type="button"] {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
  }
  
  /* Better form inputs on mobile */
  input, 
  textarea, 
  select {
    font-size: 16px; /* Prevents zoom on iOS */
    -webkit-appearance: none;
    border-radius: 8px;
  }
  
  /* Improve readability */
  body {
    font-size: 16px;
    line-height: 1.5;
  }
  
  /* Better spacing for mobile */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (max-width: 480px) {
  /* Smaller screens adjustments */
  h1 { font-size: 1.8rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.3rem; }
  h4 { font-size: 1.1rem; }
  
  /* Better button sizing */
  button, 
  .btn {
    padding: 0.8rem 1.2rem;
    font-size: 0.95rem;
  }
  
  /* Improve form spacing */
  .input-group {
    margin-bottom: 1rem;
  }
  
  /* Better modal/popup sizing */
  .modal,
  .popup {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }
}

/* Fix for iOS Safari viewport issues */
@supports (-webkit-touch-callout: none) {
  .login-wrapper,
  .register-wrapper {
    min-height: -webkit-fill-available;
  }
}

/* Improve scrolling on mobile */
@media (max-width: 768px) {
  .login-right,
  .register-right {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
  }
}

/* Better focus states for mobile */
@media (max-width: 768px) {
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid #00d4ff;
    outline-offset: 2px;
    border-color: #00d4ff;
  }
}

/* Prevent text selection issues on mobile */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Better loading states on mobile */
@media (max-width: 768px) {
  .loading {
    font-size: 0.9rem;
  }
  
  .loading-spinner {
    width: 18px;
    height: 18px;
  }
}
