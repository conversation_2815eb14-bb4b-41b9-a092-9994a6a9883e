<link rel="stylesheet" href="/css/courses.css">

 
<!-- Enhanced Hero Section -->
<section class="course-hero">
  <div class="hero-content">
    <h2>Explore Our Courses</h2>
    <p>Discover amazing courses and boost your skills with expert instructors!</p>
  </div>
</section>

<!-- Enhanced Search Section -->
<section class="course-search-section">
  <div class="search-container">
    <form id="course-search-form">
      <div class="search-input-wrapper">
        <i class="fas fa-search search-icon"></i>
        <input 
          type="text" 
          id="course-search-input" 
          placeholder="Search courses by title, instructor, or tag..." 
          autocomplete="off"
        />
        <i class="fas fa-times clear-search" id="clear-search"></i>
      </div>
      <button type="submit" id="course-search-btn">
        <i class="fas fa-search"></i>
        <span>Search</span>
      </button>
    </form>
  </div>
</section>

<!-- Results Counter -->
<div id="results-counter" class="results-counter" style="display: none;"></div>

<!-- Courses List Section -->
<section id="courses-list">
  <div id="courses-loading" class="loading-state">
    <i class="fas fa-spinner fa-spin"></i>
    <span>Loading courses...</span>
  </div>
  <div id="courses-error" class="error-state" style="display:none;"></div>
  <div id="courses-container" class="courses-grid" style="display:none;"></div>
  <div id="pagination-container" class="pagination-container" style="display:none;"></div>
  <!-- Added: Total pages and navigation info -->
  <div id="courses-pages-info" class="courses-pages-info" style="display:none; margin-top: 1em; text-align: center; color: #555; font-size: 1rem;"></div>
</section>

<script src="/js/courses.js"  ></script>
