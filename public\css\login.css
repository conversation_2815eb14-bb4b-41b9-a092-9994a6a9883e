/* Modern Login Page Styles */

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
/* Remove any default body styles that might cause gaps */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #e0e6ed;
  line-height: 1.6;
}

/* Ensure full background coverage */
body.login-page {
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  background-attachment: fixed;
  min-height: 100vh;
}

/* Main container that fills the entire viewport */
main {
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  background-attachment: fixed;
  min-height: 100vh;
  width: 100%;
  position: relative;
  margin: 0;
  padding: 0;
}

.login-wrapper {
  min-height: 100vh;
  display: flex;
  width: 100%;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  margin: 0;
  padding: 0;
}

/* Left Side - Branding/Image Section */
.login-left {
  flex: 1;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(124, 58, 237, 0.2) 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  position: relative;
  overflow: hidden;
}

.login-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(124, 58, 237, 0.15) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(-10px) rotate(-1deg); }
}

.brand-content {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 500px;
}

.brand-logo {
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: white;
  box-shadow: 0 20px 60px rgba(0, 212, 255, 0.3);
}

.brand-title {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 900;
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.brand-subtitle {
  font-size: clamp(1.1rem, 2vw, 1.3rem);
  color: rgba(224, 230, 237, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.brand-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: rgba(224, 230, 237, 0.9);
  font-size: 1rem;
}

.feature-icon {
  width: 40px;
  height: 40px;
  background: rgba(0, 212, 255, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00d4ff;
  font-size: 1.2rem;
}

/* Right Side - Form Section */
.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20px);
}

.login-container {
  width: 100%;
  max-width: 450px;
  background: rgba(26, 26, 46, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 3rem 2.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  position: relative;
  backdrop-filter: blur(20px);
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #00d4ff 0%, #7c3aed 100%);
  border-radius: 25px 25px 0 0;
}

.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.login-header h2 {
  font-size: 2rem;
  font-weight: 900;
  color: #e0e6ed;
  margin-bottom: 0.5rem;
  letter-spacing: -0.02em;
}

.login-header p {
  color: rgba(224, 230, 237, 0.7);
  font-size: 0.95rem;
  font-weight: 400;
  line-height: 1.5;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #e0e6ed;
  margin-left: 0.5rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1.2rem;
  color: rgba(0, 212, 255, 0.6);
  font-size: 1.1rem;
  z-index: 2;
  pointer-events: none;
  transition: all 0.3s ease;
}

.login-form input {
  width: 100%;
  padding: 1.2rem 1.2rem 1.2rem 3.2rem;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  font-size: 1rem;
  background: rgba(15, 15, 35, 0.8);
  color: #e0e6ed;
  outline: none;
  font-family: inherit;
  font-weight: 400;
  transition: all 0.3s ease;
}

.login-form input:focus {
  border-color: #00d4ff;
  background: rgba(15, 15, 35, 1);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.login-form input:focus + .input-icon {
  color: #00d4ff;
}

.login-form input::placeholder {
  color: rgba(224, 230, 237, 0.4);
  font-weight: 400;
}

.password-toggle {
  position: absolute;
  right: 1.2rem;
  color: rgba(224, 230, 237, 0.5);
  cursor: pointer;
  font-size: 1.1rem;
  transition: color 0.3s ease;
  z-index: 2;
}

.password-toggle:hover {
  color: #00d4ff;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: rgba(224, 230, 237, 0.7);
}

.remember-me input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin: 0;
  padding: 0;
  accent-color: #00d4ff;
}

.forgot-password {
  font-size: 0.9rem;
  color: #00d4ff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #7c3aed;
  text-decoration: underline;
}

.login-btn {
  padding: 1.2rem 0;
  border-radius: 15px;
  border: none;
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  display: none;
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.login-error, .login-success {
  border-radius: 15px;
  padding: 1rem 1.2rem;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  display: none;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fecaca;
}

.login-success {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: #a7f3d0;
}

.divider {
  margin: 2rem 0;
  text-align: center;
  position: relative;
  color: rgba(224, 230, 237, 0.5);
  font-size: 0.9rem;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.divider span {
  background: rgba(26, 26, 46, 0.9);
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

.social-login {
  display: flex;
  gap: 1rem;
}

.social-btn {
  flex: 1;
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  background: rgba(15, 15, 35, 0.8);
  color: #e0e6ed;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.social-btn:hover {
  border-color: rgba(0, 212, 255, 0.3);
  background: rgba(0, 212, 255, 0.1);
  transform: translateY(-1px);
}

.signup-link {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(224, 230, 237, 0.7);
  font-size: 0.95rem;
}

.signup-link a {
  color: #00d4ff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.signup-link a:hover {
  color: #7c3aed;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .login-left {
    padding: 2rem;
  }

  .login-right {
    padding: 2rem;
  }

  .brand-title {
    font-size: 2.5rem;
  }

  .brand-logo {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .login-wrapper {
    flex-direction: column;
    min-height: calc(100vh - 120px);
  }

  .login-left {
    min-height: 30vh;
    padding: 1.5rem 1rem;
  }

  .login-right {
    min-height: 70vh;
    padding: 1.5rem 1rem;
  }

  .brand-features {
    display: none;
  }

  .brand-logo {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
    margin-bottom: 0.8rem;
  }

  .brand-title {
    font-size: 1.8rem;
    margin-bottom: 0.3rem;
  }

  .brand-subtitle {
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
  }

  .login-container {
    padding: 1.5rem;
    max-width: 100%;
  }

  .social-login {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .login-wrapper {
    min-height: calc(100vh - 100px);
  }

  .login-left {
    min-height: 25vh;
    padding: 1rem 0.8rem;
  }

  .login-right {
    padding: 1rem 0.8rem;
    min-height: 75vh;
  }

  .login-container {
    padding: 1.2rem;
    border-radius: 20px;
    margin: 0;
  }

  .login-header h2 {
    font-size: 1.4rem;
  }

  .login-header p {
    font-size: 0.85rem;
  }

  .brand-logo {
    width: 60px;
    height: 60px;
    font-size: 1.6rem;
    margin-bottom: 0.5rem;
  }

  .brand-title {
    font-size: 1.6rem;
  }

  .brand-subtitle {
    font-size: 0.85rem;
  }

  .login-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.8rem;
  }

  .login-form input {
    padding: 0.9rem 0.9rem 0.9rem 2.8rem;
    font-size: 0.9rem;
  }

  .input-icon {
    left: 0.9rem;
    font-size: 0.9rem;
  }

  .password-toggle {
    right: 0.9rem;
    font-size: 0.9rem;
  }

  .login-btn {
    padding: 1rem 0;
    font-size: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .login-wrapper, .login-container, .logo, .login-btn, .loading-spinner, .login-error, .login-success {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}