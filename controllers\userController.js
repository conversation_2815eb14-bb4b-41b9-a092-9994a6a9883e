import { sendResponse } from '../utils/sendResponse.js';
import * as userService from '../Services/userService.js';

export async function addUser(req, res, next) {
  try {
    const { name, email, password } = req.body;
    const photo = req.file ? req.file.path : req.body.photo || "";
 
    const user = await userService.registerUser({ name, email, password, photo });
    return sendResponse(res, 201, "User created successfully", user);
  } catch (err) {
    next(err);
  }
}

export async function showAllUsers(req, res, next) {
  try {
    const users = await userService.getAllUsers();
    return sendResponse(res, 200, "Show all users", users);
  } catch (err) {
    next(err);
  }
}

export async function updateUser(req, res, next) {
  try {
    const updatedUser = await userService.updateUser(req.params.id, req.body);
    return sendResponse(res, 200, "User updated successfully", updatedUser);
  } catch (err) {
    next(err);
  }
}
export async function getUserById(req, res, next) {
  try {
    const user = await userService.getUserById(req.params.id);
    return sendResponse(res, 200, "User found successfully", user);
  } catch (err) {
    next(err);
  }
}

export async function profile(req, res, next) {
  try { 
    const user = await userService.getUserById(req.user.userId);
    return sendResponse(res, 200, "User found successfully", user);
  } catch (err) {
    next(err);
  }
}
export async function changePassword(req, res, next) {
  try {
    await userService.changePassword(req.params.id, req.body);
    return sendResponse(res, 200, "Password updated successfully");
  } catch (err) {
    next(err);
  }
}

export async function deleteUser(req, res, next) {
  try {
    await userService.deleteUser(req.params.id);
    return sendResponse(res, 200, "User deleted successfully");
  } catch (err) {
    next(err);
  }
}

export async function updateImg(req, res, next) {
  try {
    if (!req.file) {
      return res.status(400).json({ message: "Photo file is required" });
    }
    const updatedUser = await userService.updateImg(req.params.id, req.file.path);
    return res.status(200).json({
      message: "User updated successfully",
      data: updatedUser
    });
  } catch (err) {
    next(err);
  }
}