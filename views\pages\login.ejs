<link rel="stylesheet" href="/css/login.css">
<link rel="stylesheet" href="/css/auth-pages.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<div class="login-wrapper">
  <!-- Left Side - Branding -->
  <div class="login-left">
    <div class="brand-content">
      <div class="brand-logo">
        <i class="fas fa-graduation-cap"></i>
      </div>
      <h1 class="brand-title">JooCourses</h1>
      <p class="brand-subtitle">Transform your career with cutting-edge technology courses and expert mentorship</p>

      <div class="brand-features">
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-chalkboard-teacher"></i>
          </div>
          <div>
            <strong>Expert Instructors</strong><br>
            Learn from industry professionals
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-certificate"></i>
          </div>
          <div>
            <strong>Certified Learning</strong><br>
            Earn recognized certifications
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">
            <i class="fas fa-users"></i>
          </div>
          <div>
            <strong>Global Community</strong><br>
            Connect with learners worldwide
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Right Side - Login Form -->
  <div class="login-right">
    <div class="login-container">
      <div class="login-header">
        <h2>Welcome Back</h2>
        <p>Sign in to your account to continue learning</p>
      </div>
      
      <div id="login-error" class="login-error" role="alert" aria-live="polite"></div>
      <div id="login-success" class="login-success" role="alert" aria-live="polite"></div>
      
      <form id="login-form" class="login-form" autocomplete="on" novalidate>
        <div class="input-group">
          <label for="email" class="input-label">Email Address</label>
          <div class="input-wrapper">
            <span class="input-icon"><i class="fas fa-envelope"></i></span>
            <input 
              type="email" 
              id="email" 
              name="email" 
              placeholder="Enter your email address" 
              required 
              autocomplete="email"
              aria-describedby="email-error"
            />
          </div>
          <div id="email-error" class="sr-only" role="alert"></div>
        </div>
        
        <div class="input-group">
          <label for="password" class="input-label">Password</label>
          <div class="input-wrapper">
            <span class="input-icon"><i class="fas fa-lock"></i></span>
            <input 
              type="password" 
              id="password" 
              name="password" 
              placeholder="Enter your password" 
              required 
              autocomplete="current-password"
              aria-describedby="password-error"
            />
            <span class="password-toggle" onclick="togglePassword()">
              <i class="fas fa-eye" id="password-icon"></i>
            </span>
          </div>
          <div id="password-error" class="sr-only" role="alert"></div>
        </div>
        
        <div class="login-options">
          <label class="remember-me">
            <input type="checkbox" id="remember" name="remember" />
            Remember me
          </label>
          <a href="#" class="forgot-password">Forgot password?</a>
        </div>
        
        <button type="submit" class="login-btn" id="login-btn">
          <span class="loading-spinner" id="loading-spinner"></span>
          <i class="fas fa-arrow-right-to-bracket" id="login-icon"></i>
          <span id="btn-text">Sign In</span>
        </button>
      </form>
       
      
      <div class="signup-link">
        Don't have an account? <a href="/register">Sign up here</a>
      </div>
    </div>
  </div>
</div>

<script src="/js/login.js"></script>