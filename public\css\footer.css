.site-footer {
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.98) 0%, rgba(26, 26, 46, 0.95) 100%);
    border-top: 1px solid rgba(0, 212, 255, 0.2);
    color: #e0e6ed;
    padding: 0.8rem 1rem 0.4rem 1rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    box-shadow: 0 -8px 32px rgba(0,0,0,0.4);
    margin-top: 0;
    position: relative;
    backdrop-filter: blur(20px);
    width: 100%;
    display: block;
    min-height: auto;
}

.site-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.5), transparent);
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    gap: 2rem;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-logo {
    font-size: 1.8rem;
    font-weight: 900;
    background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    margin-bottom: 0.3rem;
}

.footer-brand p {
    color: rgba(224, 230, 237, 0.7);
    font-size: 0.9rem;
    margin: 0;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
    justify-content: center;
}

.footer-link {
    color: rgba(224, 230, 237, 0.8);
    text-decoration: none;
    padding: 0.7rem 1rem;
    border-radius: 25px;
    font-weight: 500;
    font-size: 0.9rem;
    background: transparent;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.footer-link:hover {
    color: #00d4ff;
    transform: translateY(-1px);
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.3);
}

.footer-link svg {
    transition: transform 0.3s ease;
}

.footer-link:hover svg {
    transform: scale(1.1);
}

.footer-extra {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: flex-end;
    font-size: 0.9rem;
    color: rgba(224, 230, 237, 0.6);
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .footer-links {
        justify-content: center;
        gap: 1rem;
    }

    .footer-extra {
        justify-content: center;
        font-size: 0.8rem;
    }

    .site-footer {
        padding: 1rem 1rem 0.5rem 1rem;
    }
}

@media (max-width: 480px) {
    .footer-links {
        flex-direction: column;
        gap: 0.8rem;
    }

    .footer-link {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
    }

    .footer-logo {
        font-size: 1.5rem;
    }

    .footer-extra {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .site-footer {
           padding: 1rem 1rem 0.5rem 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .footer-logo {
        font-size: 1.3rem;
        margin-bottom: 0.5rem;
    }

    .footer-links {
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .footer-link {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .footer-extra {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
        font-size: 0.85rem;
    }

    .footer-social {
        gap: 1rem;
    }

    .footer-social a {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .site-footer {
        padding: 0.8rem 0.8rem 0.4rem 0.8rem;
    }

    .footer-content {
        gap: 0.8rem;
    }

    .footer-logo {
        font-size: 1.2rem;
    }

    .footer-links {
        gap: 0.3rem;
    }

    .footer-link {
        font-size: 0.85rem;
        padding: 0.3rem 0.6rem;
    }

    .footer-extra {
        gap: 0.6rem;
        font-size: 0.8rem;
    }

    .footer-social a {
        font-size: 1.1rem;
        padding: 0.4rem;
    }
}